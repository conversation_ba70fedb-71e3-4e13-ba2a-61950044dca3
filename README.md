# LaraLab Print Agent

A professional label printing service for laboratory management systems.

## 🚀 Quick Start

### For End Users
1. Download `LaraLabPrintAgent_v1.1_English_Final.exe`
2. Run as administrator
3. Follow installation wizard
4. Configure printer in settings
5. Done! The service runs automatically

### For Developers
```bash
# Clone and setup
git clone <repository>
cd print_agent_new

# Install dependencies
pip install -r requirements.txt

# Run development version
python gui.py
```

## 📁 Project Structure

```
print_agent_new/
├── gui.py                    # Main GUI application
├── server.py                 # Flask web server
├── printer_core.py           # Core printing logic
├── label_design.py           # Label design and generation
├── config.json               # Default configuration
├── build_installer.py        # Build script
├── simple_installer.iss      # Inno Setup installer script
├── simple_print_test.py      # Print testing utility
├── add_to_startup.py         # Windows startup utility
├── requirements.txt          # Python dependencies
├── fonts/                    # Arabic fonts
│   ├── NotoNaskhArabic-Bold.ttf
│   └── NotoNaskhArabic-Regular.ttf
├── BUILD_INSTRUCTIONS.md     # Detailed build guide
└── TROUBLESHOOTING.md        # Common issues and solutions
```

## 🔧 Features

- **Multi-language Support**: Arabic and English text rendering
- **Print Spooler Management**: Automatic detection and fixing of Windows print service issues
- **Flexible Configuration**: Easy printer and port configuration
- **Error Handling**: Comprehensive error handling with clear messages
- **Auto-startup**: Optional Windows startup integration
- **Professional Installer**: Inno Setup based installer

## 🛠️ Building

### Quick Build
```bash
python build_installer.py
```

### Manual Build
```bash
# Build executable
pyinstaller --onefile --windowed --name=LaraLabPrintAgent --add-data="fonts;fonts" --add-data="config.json;." gui.py

# Create installer
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" simple_installer.iss
```

## 📋 Requirements

- Windows 7/8/10/11
- Python 3.8+ (for development)
- Print Spooler service enabled
- At least one printer installed

## 🔍 Troubleshooting

Common issues and solutions are documented in [TROUBLESHOOTING.md](TROUBLESHOOTING.md).

### Quick Fixes

**Settings window not showing:**
- Run as administrator
- Check Print Spooler service
- Delete config file and restart

**RPC Server Unavailable (1722):**
- Restart Print Spooler service
- Run: `net start spooler`

**Print issues:**
- Check printer connection
- Update printer drivers
- Test with Windows print test page

## 📞 Support

For technical support, check:
- [TROUBLESHOOTING.md](TROUBLESHOOTING.md) - Comprehensive troubleshooting guide
- [BUILD_INSTRUCTIONS.md](BUILD_INSTRUCTIONS.md) - Detailed build instructions
- Run `simple_print_test.py` for print testing

## 📄 License

This project is proprietary software for LaraLab systems.
