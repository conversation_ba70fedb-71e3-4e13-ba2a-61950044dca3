#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار طباعة مبسط - لاختبار الطباعة بدون الخادم
"""

import queue
import json
from printer_core import load_config, process_print_request

def test_simple_print():
    """Simple print test with sample data"""

    print("🧪 Print Test")
    print("=" * 30)

    # Create log queue
    log_queue = queue.Queue()

    # Load configuration
    config = load_config()
    print(f"📄 Selected printer: {config.get('printer_name', 'Not specified')}")

    # Test data
    test_data = {
        "labOrder": {
            "patient": {
                "name": "<PERSON>"  # Name containing Arabic characters
            },
            "created_at_raw": "2024-01-15T10:30:00Z"
        },
        "sampleGroups": {
            "group1": {
                "sample_code": "TEST-001",
                "name": "Serum",
                "tests": [
                    {"name": "CBC"},
                    {"name": "Glucose"},
                    {"name": "Complete Blood Count"},
                    {"name": "Creatinine"},
                    {"name": "Liver Function Test"},
                    {"name": "AST"},
                    {"name": "Cholesterol Total"}
                ]
            }
        }
    }

    print("📋 Data:")
    print(f"   Patient: {test_data['labOrder']['patient']['name']}")
    print(f"   Sample Code: {test_data['sampleGroups']['group1']['sample_code']}")
    print()

    # Run printing
    print("🖨️  Starting print...")
    try:
        success = process_print_request(test_data, config, log_queue)

        if success:
            print("✅ Print successful!")
        else:
            print("❌ Print failed!")

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_simple_print()
