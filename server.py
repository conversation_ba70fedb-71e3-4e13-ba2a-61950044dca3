from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import json # Added for robust data parsing

# إعداد تسجيل الدخول الأساسي
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def start_server(log_queue, config, print_callback):
    app = Flask(__name__)
    CORS(app)  # السماح بطلبات Cross-Origin

    @app.route('/print-labels', methods=['POST'])
    def handle_print_request():
        log_queue.put("INFO: New print request received...")

        # Check that content type is JSON
        # if not request.is_json:
        #     log_queue.put("ERROR: Received request is not JSON format.")
        #     return jsonify({"status": "error", "message": "Invalid content type, expected application/json"}), 400

        raw_data = request.get_json()

        # --- Robust Data Parsing ---
        # Handle case where JSON is sent as stringified JSON
        if isinstance(raw_data, str):
            try:
                data = json.loads(raw_data)
            except json.JSONDecodeError:
                log_queue.put("ERROR: Received data is text that cannot be parsed as JSON.")
                return jsonify({"status": "error", "message": "Received string is not valid JSON."}), 400
        else:
            data = raw_data

        # Validate basic data
        if not isinstance(data, dict) or 'labOrder' not in data or 'sampleGroups' not in data:
            log_queue.put("ERROR: Received data is incomplete. Fields 'labOrder' and 'sampleGroups' are required.")
            return jsonify({"status": "error", "message": "Missing required data: 'labOrder' and 'sampleGroups'"}), 400

        log_queue.put(f"INFO: Print request for patient: {data.get('labOrder', {}).get('patient', {}).get('name', 'Unknown')}")
        
        try:
            # Call actual print function and pass data
            success = print_callback(data)
            if success:
                log_queue.put("INFO: Print job sent successfully.")
                return jsonify({"status": "success", "message": "Print job processed successfully"})
            else:
                log_queue.put("ERROR: Printing failed. Check log for more details.")
                return jsonify({"status": "error", "message": "Printing failed"}), 500
        except Exception as e:
            log_queue.put(f"ERROR: Critical error during printing: {e}")
            logging.error(f"Critical error during printing: {e}", exc_info=True)
            return jsonify({"status": "error", "message": f"An internal error occurred: {e}"}), 500

    @app.route('/health-check', methods=['GET'])
    def health_check():
        return jsonify({"status": "ok", "message": "Server is running"})

    try:
        port = int(config.get('listening_port', 9898))
        log_queue.put(f"INFO: Starting server on port {port}...")
        # Use '0.0.0.0' to make server available on local network
        app.run(host='0.0.0.0', port=port)
    except Exception as e:
        log_queue.put(f"ERROR: Failed to start server: {e}")
        logging.error(f"Failed to start Flask server: {e}", exc_info=True)

if __name__ == '__main__':
    # هذا الجزء للاختبار المباشر للملف
    import queue
    test_queue = queue.Queue()
    test_config = {'listening_port': 9898}
    
    def dummy_print(data):
        print("--- DUMMY PRINT ---")
        print(f"Patient: {data['labOrder']['patient']['name']}")
        for group in data['sampleGroups']:
            print(f"  - Sample: {group['sample_code']} ({group['name']})")
        print("-------------------")
        # محاكاة النجاح
        return True

    # طباعة رسائل الاختبار من الطابور
    def log_consumer(q):
        while True:
            try:
                msg = q.get_nowait()
                print(f"LOG: {msg}")
            except queue.Empty:
                pass

    import threading
    threading.Thread(target=log_consumer, args=(test_queue,), daemon=True).start()

    start_server(test_queue, test_config, dummy_print) 