import requests
import json
import platform
import sys
import os
import traceback
from label_design import create_compact_label_image
import win32con

if platform.system() == "Windows":
    import win32print
    import win32ui
    from PIL import ImageWin
else:
    raise RuntimeError("هذا البرنامج يعمل فقط على ويندوز")

# استخدام مجلد AppData كما في gui.py
if platform.system() == "Windows":
    CONFIG_DIR = os.path.join(os.environ["APPDATA"], "LaraLabPrintAgent")
else:
    CONFIG_DIR = os.path.join(os.path.expanduser("~"), ".laralabprintagent")
    
if not os.path.exists(CONFIG_DIR):
    os.makedirs(CONFIG_DIR)
    
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")

def resource_path(relative_path):
    try:
        base_path = sys._MEIPASS
    except AttributeError:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def load_config():
    try:
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            config = json.load(f)
            # التأكد من وجود مفاتيح افتراضية جديدة
            if 'listening_port' not in config:
                config['listening_port'] = 9898
        return config
    except Exception as e:
        print(f"❌ Error reading config file ({CONFIG_FILE}): {e}")
        # Default settings if file cannot be read
        default_config = {
            "printer_name": "",
            "label_width_mm": 28,
            "label_height_mm": 24,
            "dpi": 300,
            "listening_port": 9898,
        }
        return default_config

def check_printer(printer_name):
    try:
        printers = [printer[2] for printer in win32print.EnumPrinters(2)]
        return printer_name in printers, printers
    except Exception as e:
        print(f"❌ Error checking printers: {e}")
        # If query fails, assume printer exists if name was entered manually
        return True, []

def check_fonts(font_family):
    font_path = resource_path(os.path.join("fonts", font_family))
    return os.path.exists(font_path)

def check_print_spooler_service():
    """Check Print Spooler service status"""
    try:
        import subprocess
        result = subprocess.run(['sc', 'query', 'spooler'],
                              capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            output = result.stdout
            if "RUNNING" in output:
                return True, "Print service is running normally"
            elif "STOPPED" in output:
                return False, "Print service is stopped"
            else:
                return False, f"Print service status unclear: {output}"
        else:
            return False, f"Cannot check print service status: {result.stderr}"

    except Exception as e:
        return False, f"Error checking print service: {e}"

def start_print_spooler_service():
    """Attempt to start Print Spooler service"""
    try:
        import subprocess
        result = subprocess.run(['net', 'start', 'spooler'],
                              capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            return True, "Print service started successfully"
        else:
            return False, f"Failed to start print service: {result.stderr}"

    except Exception as e:
        return False, f"Error starting print service: {e}"

def print_image(image, printer_name, log_queue, config=None):
    if not printer_name:
        log_queue.put("ERROR: ❌ Printer name is empty - make sure to select printer in settings")
        return False

    # Check Print Spooler service status first
    spooler_running, spooler_message = check_print_spooler_service()
    if not spooler_running:
        log_queue.put(f"ERROR: ❌ {spooler_message}")
        log_queue.put("INFO: 🔧 Attempting to start print service...")

        # Try to start the service
        started, start_message = start_print_spooler_service()
        if started:
            log_queue.put(f"INFO: ✅ {start_message}")
            # Wait a bit to ensure service starts
            import time
            time.sleep(2)
        else:
            log_queue.put(f"ERROR: ❌ {start_message}")
            log_queue.put("INFO: 🔧 Manual solutions:")
            log_queue.put("INFO: 1. Open Command Prompt as administrator")
            log_queue.put("INFO: 2. Run command: net start spooler")
            log_queue.put("INFO: 3. Or open Services.msc and start Print Spooler")
            return False
    
    # Check printer availability (with RPC error handling)
    try:
        printers = [printer[2] for printer in win32print.EnumPrinters(2)]
        if printer_name not in printers:
            log_queue.put(f"WARN: ⚠️ Printer '{printer_name}' not found in list, but will try to print anyway")
            available_printers = ", ".join(printers)
            log_queue.put(f"INFO: Available printers: {available_printers}")
            # Don't stop here, try printing anyway
    except Exception as e:
        log_queue.put(f"WARN: ⚠️ Cannot get printer list: {e}")
        log_queue.put(f"INFO: Will try to print to specified printer: {printer_name}")
    
    try:
        # Open printer and get device context
        try:
            hprinter = win32print.OpenPrinter(printer_name)
        except Exception as e:
            if "1722" in str(e) or "RPC" in str(e).upper():
                log_queue.put("ERROR: ❌ Print Spooler service is stopped or disabled!")
                log_queue.put("INFO: 🔧 Suggested solutions:")
                log_queue.put("INFO: 1. Open Services.msc")
                log_queue.put("INFO: 2. Find 'Print Spooler'")
                log_queue.put("INFO: 3. Right-click and select 'Start'")
                log_queue.put("INFO: 4. Or run command: net start spooler")
                return False
            else:
                log_queue.put(f"ERROR: ❌ Cannot open printer '{printer_name}': {e}")
                return False

        try:
            pdc = win32ui.CreateDC()
            pdc.CreatePrinterDC(printer_name)
        except Exception as e:
            win32print.ClosePrinter(hprinter)
            log_queue.put(f"ERROR: ❌ Cannot create print context: {e}")
            return False
        
        # Get actual printer information
        printer_dpi_x = pdc.GetDeviceCaps(win32con.LOGPIXELSX)
        printer_dpi_y = pdc.GetDeviceCaps(win32con.LOGPIXELSY)
        physical_width = pdc.GetDeviceCaps(win32con.PHYSICALWIDTH)
        physical_height = pdc.GetDeviceCaps(win32con.PHYSICALHEIGHT)
        printable_width = pdc.GetDeviceCaps(win32con.HORZRES)
        printable_height = pdc.GetDeviceCaps(win32con.VERTRES)

        # Required dimensions in millimeters
        TARGET_WIDTH_MM = 50.0
        TARGET_HEIGHT_MM = 25.0

        # Convert required dimensions to printer points
        width_printer_px = int(TARGET_WIDTH_MM * printer_dpi_x / 25.4)
        height_printer_px = int(TARGET_HEIGHT_MM * printer_dpi_y / 25.4)

        # Calculate scaling factors
        scale_x = width_printer_px / image.width
        scale_y = height_printer_px / image.height
        
        try:
            # Start print job
            pdc.StartDoc("Sticker Print")
            pdc.StartPage()

            # Improve print quality for Arabic text (simplified)
            dib = ImageWin.Dib(image)

            # Set print quality
            pdc.SetMapMode(win32con.MM_TEXT)

            # Fix non-printable area issue - add safe margin for printing
            printer_margin_mm = config.get('printer_margin_mm', 0.1) if config else 0.1
            printer_margin_px = int(printer_dpi_x * printer_margin_mm / 25.4)

            # Print without stretching if dimensions are approximately matching
            if abs(image.width - width_printer_px) < 10 and abs(image.height - height_printer_px) < 10:
                # Direct print with safe margin
                safe_x = printer_margin_px
                safe_y = printer_margin_px
                safe_width = image.width - (2 * printer_margin_px)
                safe_height = image.height - (2 * printer_margin_px)
                dib.draw(pdc.GetHandleOutput(), (safe_x, safe_y, safe_x + safe_width, safe_y + safe_height))
                log_queue.put("DEBUG: Direct print with safe printer margin")
            else:
                # Print with scaling and safe margin
                safe_x = printer_margin_px
                safe_y = printer_margin_px
                safe_width = width_printer_px - (2 * printer_margin_px)
                safe_height = height_printer_px - (2 * printer_margin_px)
                dib.draw(pdc.GetHandleOutput(), (safe_x, safe_y, safe_x + safe_width, safe_y + safe_height))
                log_queue.put("DEBUG: Print with scaling and safe printer margin")
            
            pdc.EndPage()
            pdc.EndDoc()
            pdc.DeleteDC()
            
            # Log diagnostic information
            log_queue.put(f"INFO: ✅ Print completed successfully on '{printer_name}'")
            log_queue.put(f"DEBUG: Required dimensions: {TARGET_WIDTH_MM:.1f}mm × {TARGET_HEIGHT_MM:.1f}mm")
            log_queue.put(f"DEBUG: Printer DPI: {printer_dpi_x}×{printer_dpi_y} DPI")
            log_queue.put(f"DEBUG: Applied printer margin: {printer_margin_px} pixels ({printer_margin_mm:.1f}mm)")
            log_queue.put(f"DEBUG: Image dimensions: {image.width}×{image.height} pixels")
            log_queue.put(f"DEBUG: Printer dimensions: {width_printer_px}×{height_printer_px} pixels")
            log_queue.put(f"DEBUG: Actual print dimensions: {width_printer_px/printer_dpi_x*25.4:.1f}mm × {height_printer_px/printer_dpi_y*25.4:.1f}mm")
            log_queue.put(f"DEBUG: Available print area: {printable_width/printer_dpi_x*25.4:.1f}mm × {printable_height/printer_dpi_y*25.4:.1f}mm")
            return True
            
        finally:
            win32print.ClosePrinter(hprinter)
            
    except Exception as e:
        error_details = traceback.format_exc()
        log_queue.put(f"ERROR: ❌ Print error: {e}")
        log_queue.put(f"DEBUG: Error details: {error_details}")
        return False

def process_print_request(data, config, log_queue):
    """
    Processes a full print request received from the server.
    Iterates through sample groups and prints one label for each.
    """
    all_successful = True
    try:
        lab_order_info = data['labOrder']
        sample_groups_dict = data['sampleGroups']
        
        # Ensure sampleGroups is a dictionary
        if not isinstance(sample_groups_dict, dict):
            log_queue.put(f"ERROR: ❌ Field 'sampleGroups' must be an object (dictionary), but received type: {type(sample_groups_dict)}")
            return False

        # Convert dictionary values to list for processing
        sample_groups = list(sample_groups_dict.values())
        printer_name = config.get("printer_name")

        # Convert dimensions from mm to pixels - use actual printer DPI
        # Get printer DPI first
        try:
            hprinter = win32print.OpenPrinter(printer_name)
            pdc = win32ui.CreateDC()
            pdc.CreatePrinterDC(printer_name)
            printer_dpi_x = pdc.GetDeviceCaps(win32con.LOGPIXELSX)
            printer_dpi_y = pdc.GetDeviceCaps(win32con.LOGPIXELSY)
            pdc.DeleteDC()
            win32print.ClosePrinter(hprinter)
        except Exception as e:
            if "1722" in str(e) or "RPC" in str(e).upper():
                log_queue.put("ERROR: ❌ Print Spooler service is stopped!")
                log_queue.put("INFO: 🔧 Please start Print Spooler service first")
                return False
            else:
                log_queue.put(f"WARN: ⚠️ Cannot get printer DPI, using default value: {e}")
                # Use default DPI
                printer_dpi_x = printer_dpi_y = config.get("dpi", 300)

        # Use actual printer DPI to create image
        actual_dpi = min(printer_dpi_x, printer_dpi_y)  # Use lower DPI for compatibility
        config_with_printer_dpi = config.copy()
        config_with_printer_dpi["dpi"] = actual_dpi

        log_queue.put(f"INFO: Using actual printer DPI: {actual_dpi} DPI")

        log_queue.put(f"INFO: Starting to process print request containing {len(sample_groups)} label(s).")

        for i, group in enumerate(sample_groups):
            sample_code = group.get('sample_code', f"Label {i+1}")
            log_queue.put(f"INFO: -> Creating label for: {sample_code}")

            # Create label image using actual printer DPI
            label_image = create_compact_label_image(
                lab_order_info=lab_order_info,
                sample_group=group,
                config=config_with_printer_dpi
            )

            # Print the image
            if not print_image(label_image, printer_name, log_queue, config):
                log_queue.put(f"ERROR: ❌ Failed to print label for {sample_code}.")
                all_successful = False
                # You can decide to stop printing on first error or continue
                # break

        if all_successful:
            log_queue.put("INFO: ✅ All print jobs completed successfully.")
        else:
            log_queue.put("WARN: ⚠️ Request processing completed with errors in some labels.")

    except KeyError as e:
        log_queue.put(f"ERROR: ❌ Error in received data, expected field missing: {e}")
        all_successful = False
    except Exception as e:
        log_queue.put(f"ERROR: ❌ Unexpected error occurred while creating label: {e}")
        log_queue.put(f"DEBUG: {traceback.format_exc()}")
        all_successful = False
        
    return all_successful 