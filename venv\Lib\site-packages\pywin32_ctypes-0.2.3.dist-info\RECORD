pywin32_ctypes-0.2.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pywin32_ctypes-0.2.3.dist-info/LICENSE.txt,sha256=36g7PicJrfzbg42a1VgjymdKu3gOYFY9ndlUTMv3hek,1644
pywin32_ctypes-0.2.3.dist-info/METADATA,sha256=Hjdn0_r_TVucNHFgiDgxCY0fJaz0MGsCyazcxr-1iDs,3909
pywin32_ctypes-0.2.3.dist-info/RECORD,,
pywin32_ctypes-0.2.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pywin32_ctypes-0.2.3.dist-info/WHEEL,sha256=OqRkF0eY5GHssMorFjlbTIq072vpHpF60fIQA6lS9xA,92
pywin32_ctypes-0.2.3.dist-info/top_level.txt,sha256=Q67ar0C8ghsHWr96rJ8iA0mLCxbYQLxeS5fHmMODw0k,12
win32ctypes/__init__.py,sha256=EmS7iTWm0dQUortMjTA5JEWYIH25AUBUaDPhppFfTHo,214
win32ctypes/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/__pycache__/pywintypes.cpython-312.pyc,,
win32ctypes/__pycache__/version.cpython-312.pyc,,
win32ctypes/__pycache__/win32api.cpython-312.pyc,,
win32ctypes/__pycache__/win32cred.cpython-312.pyc,,
win32ctypes/core/__init__.py,sha256=qbdg3TvIwp_CZd4nHUhGiXEw1bTKXxJHqQt-4647c1Q,1647
win32ctypes/core/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/core/__pycache__/_winerrors.cpython-312.pyc,,
win32ctypes/core/__pycache__/compat.cpython-312.pyc,,
win32ctypes/core/_winerrors.py,sha256=p52rv5vvDGMNiRvvT_npIXWEsXPoVDMtom-gmto5k0M,199
win32ctypes/core/cffi/__init__.py,sha256=M68T4frchcWxHFZZhDm7F9Os4YbPX4RUtKBboPTsu70,270
win32ctypes/core/cffi/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_authentication.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_common.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_dll.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_nl_support.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_resource.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_system_information.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_time.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_util.cpython-312.pyc,,
win32ctypes/core/cffi/_authentication.py,sha256=oRxgigZ3mB_JTXFHIlJZxWY2b35PnQaDvEbzqJNxpzs,5333
win32ctypes/core/cffi/_common.py,sha256=tKvSSDBiYlL1gqvg1FX_RHLTYaoNWO7LJ61d78CVyf4,576
win32ctypes/core/cffi/_dll.py,sha256=GIGxlq_6hY-OiCc0pAmPcGNav-q68jdtMdoTSCgUt64,771
win32ctypes/core/cffi/_nl_support.py,sha256=FFxF4XcAF2W_fPvpwrrTpA61lYMRh03qwF3kF9dvFkw,313
win32ctypes/core/cffi/_resource.py,sha256=GABPpUTN16QiXlXqYSjkw7kpJUY-DZExfMVWc8cBRlo,4555
win32ctypes/core/cffi/_system_information.py,sha256=a_WRyYi-F9Bf299lvNMb_ZEc4zxIoL02VMj-5j_dkM8,872
win32ctypes/core/cffi/_time.py,sha256=yEUTm7TIx_d2Me1vOFVsubOR_8Xmsob6e80n6dj9wYI,332
win32ctypes/core/cffi/_util.py,sha256=qwc1wkFdryhvOPZf1W7nWZMkuS2o8GuPpQM2-sQCDUY,2662
win32ctypes/core/compat.py,sha256=XPSOaAxQA4dLn0knXO9wferQb6IpVVA56AKa95W5MkU,158
win32ctypes/core/ctypes/__init__.py,sha256=HeNUjdP0Bj5SHeb_cVMqCZa00BUTHD8E1DqFq0wpaFk,272
win32ctypes/core/ctypes/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_authentication.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_common.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_dll.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_nl_support.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_resource.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_system_information.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_time.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_util.cpython-312.pyc,,
win32ctypes/core/ctypes/_authentication.py,sha256=vcNber8K_MUWDgzieTiV4wVEWl1wwTBevnMKpf6SDTM,3822
win32ctypes/core/ctypes/_common.py,sha256=xPyUGrNusRGHPJcstR4vxPJiXD75COovAWo0EsCX7ro,1223
win32ctypes/core/ctypes/_dll.py,sha256=mfEhaV7Nbp8pok0xg0O6GhasE6oSOOxb4cgKPe38FTg,552
win32ctypes/core/ctypes/_nl_support.py,sha256=ST6YPzpCVq1JqiXVxEoULUTRFl_rBjMF_1qPNmsLqlU,315
win32ctypes/core/ctypes/_resource.py,sha256=YzmBvf04Gd0pWLt0i2cIQYmaBcBW4X2Qu--sbk4331A,4264
win32ctypes/core/ctypes/_system_information.py,sha256=TDrldTP4LTrOOGe6UVLKdFkrRwE428BWVTE2GPNHVMo,941
win32ctypes/core/ctypes/_time.py,sha256=0XYWMiahecwh2VgCLdfoHxFqw-bufX4_RCA2sr4CSxI,342
win32ctypes/core/ctypes/_util.py,sha256=Yb5hKGpbfnHfZX6x4oYzco5iLg6AhMTa6OCeLHf8-go,2030
win32ctypes/pywin32/__init__.py,sha256=Wx5mqYeeweqoUDcHEBzrgDNLRPye2SoWk1MwUsDDkSU,354
win32ctypes/pywin32/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/pywin32/__pycache__/pywintypes.cpython-312.pyc,,
win32ctypes/pywin32/__pycache__/win32api.cpython-312.pyc,,
win32ctypes/pywin32/__pycache__/win32cred.cpython-312.pyc,,
win32ctypes/pywin32/pywintypes.py,sha256=kpOmM9lJ8w3BIP0T7j7efzxhroD7Yyp7Em80O8q1BrQ,1867
win32ctypes/pywin32/win32api.py,sha256=NZ3dUJ9bdMPBhH89EDb3s_ijljFRWvy2vFwrO6bJuA8,7724
win32ctypes/pywin32/win32cred.py,sha256=ZgSLu7NU5kAQP8a6SlHXtfTSLufAd24uO8govj1i2kA,4801
win32ctypes/pywintypes.py,sha256=wVw61R5MJfof6hExSaI4RR3cnxEZjoJarCH80NtPnr8,350
win32ctypes/tests/__init__.py,sha256=sr6UZPhlGoCY5Hm7pEt8NKzXKP9YIuiIObi-HO2BLiI,693
win32ctypes/tests/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/tests/__pycache__/test_backends.cpython-312.pyc,,
win32ctypes/tests/__pycache__/test_win32api.cpython-312.pyc,,
win32ctypes/tests/__pycache__/test_win32cred.cpython-312.pyc,,
win32ctypes/tests/test_backends.py,sha256=Qjlo6GC2HHkcS31wnmvLjna2UtCym2qwR1jWWcqo_EY,1245
win32ctypes/tests/test_win32api.py,sha256=8b-ZvjAjHhUO5aCqKM2rYR_4UprSfv8AFNKSo2b5grA,11687
win32ctypes/tests/test_win32cred.py,sha256=SM__Oj6LNPzmZIOWZwBK3nHvSRNW6Nk6AO8kk9iR9bE,7948
win32ctypes/version.py,sha256=o4ScPsx3ebE9pX-fF55Y4NZwE-gZl1putS6aE2pTVtI,23
win32ctypes/win32api.py,sha256=iEIHL_pJIHauSkKXKAz0CpQ-tg9lR9YJzrV68jAVjq0,346
win32ctypes/win32cred.py,sha256=NNXVc3-4Cot0xxwJmTFzY9B6H6YEX6egQW-kbmWFkf8,348
