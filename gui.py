import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import platform
import threading
import time
import queue
import traceback
from server import start_server
from printer_core import check_printer, load_config, process_print_request

# معالجة استيراد win32print مع التعامل مع الأخطاء
try:
    import win32print
    WIN32_AVAILABLE = True
except ImportError as e:
    print(f"❌ تحذير: لا يمكن استيراد win32print: {e}")
    WIN32_AVAILABLE = False

# --- Configuration File Setup ---
if platform.system() == "Windows":
    CONFIG_DIR = os.path.join(os.environ["APPDATA"], "LaraLabPrintAgent")
else:
    CONFIG_DIR = os.path.join(os.path.expanduser("~"), ".laralabprintagent")
    
if not os.path.exists(CONFIG_DIR):
    os.makedirs(CONFIG_DIR)
    
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")


class SettingsDialog(tk.Toplevel):
    def __init__(self, parent, config, on_save_callback):
        try:
            super().__init__(parent)
            self.title("Program Settings")

            # التأكد من صحة الإعدادات
            if config is None:
                config = {}
            self.config_data = config.copy()
            self.on_save_callback = on_save_callback
            self.parent = parent

            # تحديد حجم النافذة بناءً على دقة الشاشة
            try:
                screen_width = self.winfo_screenwidth()
                screen_height = self.winfo_screenheight()

                # حساب حجم مناسب للنافذة
                dialog_width = min(450, int(screen_width * 0.4))
                dialog_height = min(500, int(screen_height * 0.6))

                # حساب موقع النافذة في وسط الشاشة
                x = (screen_width - dialog_width) // 2
                y = (screen_height - dialog_height) // 2

                self.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
            except Exception as e:
                print(f"❌ خطأ في تحديد حجم النافذة: {e}")
                self.geometry("450x500")

            self.resizable(True, True)  # السماح بتغيير الحجم لحل مشاكل العرض
            self.minsize(400, 350)  # حد أدنى للحجم

            self.create_widgets()

            # إعدادات النافذة
            self.transient(parent)
            self.grab_set()

            # التأكد من ظهور النافذة
            self.update_idletasks()
            self.deiconify()
            self.lift()
            self.focus_force()

        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة الإعدادات: {e}")
            if hasattr(self, 'parent') and self.parent:
                messagebox.showerror("خطأ", f"لا يمكن إنشاء نافذة الإعدادات:\n{e}", parent=self.parent)
            raise

    def create_widgets(self):
        try:
            frame = ttk.Frame(self, padding="10")
            frame.pack(fill="both", expand=True)

            row = 0
            # Printer Selection
            ttk.Label(frame, text="Label Printer:", anchor='w').grid(row=row, column=0, sticky='w', padx=5, pady=5)
            self.printer_var = tk.StringVar()
            self.printer_combo = ttk.Combobox(frame, textvariable=self.printer_var, state="readonly", width=30, justify='left')

            # معالجة أفضل لاستعلام الطابعات
            try:
                if WIN32_AVAILABLE:
                    printers = [printer[2] for printer in win32print.EnumPrinters(2)]
                else:
                    printers = []
                    print("❌ win32print not available, cannot get printer list")
            except Exception as e:
                print(f"❌ Error getting printer list: {e}")
                printers = []
                messagebox.showwarning("Warning", f"Cannot get printer list:\n{e}\n\nYou can enter printer name manually.", parent=self)

            # إعداد قائمة الطابعات أو حقل نص
            if printers:
                # إذا كانت الطابعات متاحة، استخدم Combobox
                self.printer_combo['values'] = printers
                self.printer_combo.config(state="readonly")
                saved_printer = self.config_data.get("printer_name", "")
                if saved_printer in printers:
                    self.printer_var.set(saved_printer)
                else:
                    self.printer_var.set(printers[0])
            else:
                # إذا لم تكن الطابعات متاحة، اجعل الحقل قابل للتحرير
                self.printer_combo.config(state="normal")
                saved_printer = self.config_data.get("printer_name", "")
                self.printer_var.set(saved_printer if saved_printer else "Enter printer name manually")

            self.printer_combo.grid(row=row, column=1, padx=5, pady=5, sticky='ew')
            row += 1

            # Listening Port
            ttk.Label(frame, text="Listening Port:", anchor='w').grid(row=row, column=0, sticky='w', padx=5, pady=5)
            try:
                port_value = self.config_data.get("listening_port", 9898)
                self.port_var = tk.IntVar(value=int(port_value))
            except (ValueError, TypeError):
                self.port_var = tk.IntVar(value=9898)
                print(f"❌ Invalid port value, using default: 9898")
            ttk.Entry(frame, textvariable=self.port_var, justify='left').grid(row=row, column=1, padx=5, pady=5, sticky='ew')
            row += 1

            # Label Dimensions
            ttk.Label(frame, text="Label Width (mm):", anchor='w').grid(row=row, column=0, sticky='w', padx=5, pady=5)
            try:
                width_value = self.config_data.get("label_width_mm", 28)
                self.width_var = tk.IntVar(value=int(width_value))
            except (ValueError, TypeError):
                self.width_var = tk.IntVar(value=28)
                print(f"❌ Invalid width value, using default: 28")
            ttk.Entry(frame, textvariable=self.width_var, justify='left').grid(row=row, column=1, padx=5, pady=5, sticky='ew')
            row += 1

            ttk.Label(frame, text="Label Height (mm):", anchor='w').grid(row=row, column=0, sticky='w', padx=5, pady=5)
            try:
                height_value = self.config_data.get("label_height_mm", 24)
                self.height_var = tk.IntVar(value=int(height_value))
            except (ValueError, TypeError):
                self.height_var = tk.IntVar(value=24)
                print(f"❌ Invalid height value, using default: 24")
            ttk.Entry(frame, textvariable=self.height_var, justify='left').grid(row=row, column=1, padx=5, pady=5, sticky='ew')
            row += 1

            # DPI
            ttk.Label(frame, text="Print Quality (DPI):", anchor='w').grid(row=row, column=0, sticky='w', padx=5, pady=5)
            try:
                dpi_value = self.config_data.get("dpi", 300)
                self.dpi_var = tk.IntVar(value=int(dpi_value))
            except (ValueError, TypeError):
                self.dpi_var = tk.IntVar(value=300)
                print(f"❌ Invalid DPI value, using default: 300")
            ttk.Entry(frame, textvariable=self.dpi_var, justify='left').grid(row=row, column=1, padx=5, pady=5, sticky='ew')
            row += 1

            # Config file path (for reference)
            ttk.Label(frame, text="Config File Path:", anchor='w').grid(row=row, column=0, sticky='w', padx=5, pady=10)
            path_label = ttk.Label(frame, text=CONFIG_FILE, foreground="blue", anchor='w', justify='left', wraplength=300)
            path_label.grid(row=row, column=1, padx=5, pady=10, sticky='ew')
            row += 1

            # Configure column weights for proper resizing
            frame.columnconfigure(1, weight=1)

            # Save and Cancel buttons
            btn_frame = ttk.Frame(frame)
            btn_frame.grid(row=row, column=0, columnspan=2, pady=10)
            ttk.Button(btn_frame, text="Save Settings", command=self.save_settings).pack(side="right", padx=10)
            ttk.Button(btn_frame, text="Cancel", command=self.destroy).pack(side="right")

        except Exception as e:
            print(f"❌ Error creating settings interface: {e}")
            messagebox.showerror("Error", f"Error creating settings window:\n{e}", parent=self.parent)
            self.destroy()

    def save_settings(self):
        old_port = self.config_data.get("listening_port")
        new_port = self.port_var.get()
        
        self.config_data["printer_name"] = self.printer_var.get()
        self.config_data["listening_port"] = new_port
        self.config_data["label_width_mm"] = self.width_var.get()
        self.config_data["label_height_mm"] = self.height_var.get()
        self.config_data["dpi"] = self.dpi_var.get()
        
        try:
            with open(CONFIG_FILE, "w", encoding="utf-8") as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=4)
            
            messagebox.showinfo("Saved", "Settings saved successfully.", parent=self)

            if old_port != new_port:
                messagebox.showinfo("Restart Required", "Program must be restarted to apply port changes.", parent=self)

            self.on_save_callback(self.config_data)
            self.destroy()
        except Exception as e:
            messagebox.showerror("Save Error", f"❌ Error saving settings: {e}", parent=self)


class StickerPrinterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("LaraLab Print Agent")
        self.root.geometry("600x450")
        self.root.resizable(True, True)
        self.root.minsize(500, 400)

        # Improve display settings
        try:
            # Determine display scale based on screen resolution
            screen_width = self.root.winfo_screenwidth()
            if screen_width > 1920:  # High resolution screens
                scaling_factor = 1.4
            elif screen_width > 1366:  # Medium resolution screens
                scaling_factor = 1.2
            else:  # Low resolution screens
                scaling_factor = 1.0

            self.root.tk.call('tk', 'scaling', scaling_factor)

            # Improve Arabic font display
            self.root.option_add('*Font', 'Tahoma 9')

        except Exception as e:
            print(f"❌ Error in display settings: {e}")
            # Use default settings
            try:
                self.root.tk.call('tk', 'scaling', 1.0)
            except:
                pass
        
        self.config = load_config()
        self.log_queue = queue.Queue()
        self.server_thread = None

        self.create_menu()
        self.create_main_widgets()
        self.create_status_bar()
        
        self.start_server_thread()
        self.process_log_queue()
        
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Menu", menu=settings_menu)
        settings_menu.add_command(label="Settings", command=self.open_settings_dialog)
        settings_menu.add_separator()
        settings_menu.add_command(label="Exit", command=self.on_close)

    def create_main_widgets(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill="both", expand=True)

        log_frame = ttk.LabelFrame(main_frame, text="Event Log", padding="10")
        log_frame.pack(fill="both", expand=True)

        self.log_text = tk.Text(log_frame, height=10, width=70, state='disabled', font=("Tahoma", 9), wrap='word')
        log_scroll = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text['yscrollcommand'] = log_scroll.set
        
        self.log_text.pack(side="left", fill="both", expand=True)
        log_scroll.pack(side="right", fill="y")
        
        self.log_text.tag_config("INFO", foreground="black")
        self.log_text.tag_config("ERROR", foreground="red")
        self.log_text.tag_config("WARN", foreground="orange")
        self.log_text.tag_config("DEBUG", foreground="grey")

    def create_status_bar(self):
        status_bar = ttk.Frame(self.root, relief="sunken", padding="2 5")
        status_bar.pack(side="bottom", fill="x")
        self.server_status_label = ttk.Label(status_bar, text="Status: Starting...", anchor="w")
        self.server_status_label.pack(side="left")

    def open_settings_dialog(self):
        try:
            # Ensure main window is available
            if not self.root.winfo_exists():
                self.log("ERROR: Main window not available")
                return

            # Ensure settings exist
            if not hasattr(self, 'config') or self.config is None:
                self.log("WARN: No settings found, using default settings")
                self.config = {
                    "printer_name": "",
                    "label_width_mm": 28,
                    "label_height_mm": 24,
                    "dpi": 300,
                    "listening_port": 9898
                }

            # Create settings window
            settings_dialog = SettingsDialog(self.root, self.config, self.on_settings_saved)

            # Ensure window appears in foreground
            settings_dialog.lift()
            settings_dialog.attributes('-topmost', True)
            settings_dialog.after_idle(lambda: settings_dialog.attributes('-topmost', False))

        except Exception as e:
            error_msg = f"❌ Error opening settings window: {e}"
            print(error_msg)
            self.log(f"ERROR: {error_msg}")
            messagebox.showerror("Error", f"Cannot open settings window:\n{e}")

            # Try to create simple settings dialog as fallback
            try:
                self.create_simple_settings_dialog()
            except Exception as e2:
                print(f"❌ Failed to create fallback settings window: {e2}")
                messagebox.showerror("Critical Error", "Cannot open any settings window. Please restart the program.")

    def create_simple_settings_dialog(self):
        """Simple settings window as fallback if main window fails"""
        simple_dialog = tk.Toplevel(self.root)
        simple_dialog.title("Simple Settings")
        simple_dialog.geometry("400x300")
        simple_dialog.resizable(False, False)

        # Main frame
        main_frame = ttk.Frame(simple_dialog, padding="20")
        main_frame.pack(fill="both", expand=True)

        # Explanatory message
        info_label = ttk.Label(main_frame, text="Simple Settings Window\nYou can edit the settings file directly",
                              justify="center", font=("Tahoma", 10))
        info_label.pack(pady=10)

        # Show config file path
        path_frame = ttk.Frame(main_frame)
        path_frame.pack(fill="x", pady=10)
        ttk.Label(path_frame, text="Config File Path:").pack()
        path_entry = ttk.Entry(path_frame, width=50)
        path_entry.insert(0, CONFIG_FILE)
        path_entry.config(state="readonly")
        path_entry.pack(pady=5)

        # Buttons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(pady=20)

        def open_config_file():
            try:
                os.startfile(CONFIG_FILE)
            except Exception as e:
                messagebox.showerror("Error", f"Cannot open config file:\n{e}")

        def open_config_folder():
            try:
                os.startfile(CONFIG_DIR)
            except Exception as e:
                messagebox.showerror("Error", f"Cannot open config folder:\n{e}")

        ttk.Button(btn_frame, text="Open Config File", command=open_config_file).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="Open Config Folder", command=open_config_folder).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="Close", command=simple_dialog.destroy).pack(side="right", padx=5)

        # Bring window to front
        simple_dialog.transient(self.root)
        simple_dialog.grab_set()
        simple_dialog.focus()

    def on_settings_saved(self, new_config):
        self.config = new_config
        self.log("INFO: ✅ Settings updated.")

    def log(self, message):
        if not message: return
        
        log_level = "INFO" # Default
        if ":" in message:
            parts = message.split(":", 1)
            level = parts[0].upper()
            if level in ["INFO", "ERROR", "WARN", "DEBUG"]:
                log_level = level
                message = parts[1].strip()

        timestamp = time.strftime('%H:%M:%S')
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.configure(state='normal')
        self.log_text.insert(tk.END, formatted_message, log_level)
        self.log_text.configure(state='disabled')
        self.log_text.see(tk.END)

    def process_log_queue(self):
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.log(message)
        except queue.Empty:
            pass
        
        # Update server status
        if self.server_thread and self.server_thread.is_alive():
            port = self.config.get('listening_port', 'N/A')
            self.server_status_label.config(text=f"Status: Server running on port {port}", foreground="green")
        else:
            self.server_status_label.config(text="Status: Server stopped", foreground="red")
            
        self.root.after(250, self.process_log_queue)

    def start_server_thread(self):
        if self.server_thread and self.server_thread.is_alive():
            self.log("WARN: Server is already running.")
            return

        # The callback now includes the config and log_queue
        print_callback = lambda data: process_print_request(data, self.config, self.log_queue)

        self.server_thread = threading.Thread(
            target=start_server,
            args=(self.log_queue, self.config, print_callback),
            daemon=True
        )
        self.server_thread.start()
        self.log("INFO: Server start command issued.")

    def on_close(self):
        if messagebox.askokcancel("Exit", "Are you sure you want to exit? This will stop the print server."):
            # We don't need to explicitly stop the daemon thread, it will exit with the main app.
            self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = StickerPrinterApp(root)
    root.mainloop() 