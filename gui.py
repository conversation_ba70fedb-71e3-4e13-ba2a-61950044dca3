import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import platform
import threading
import time
import queue
import traceback
from server import start_server
from printer_core import check_printer, load_config, process_print_request

# معالجة استيراد win32print مع التعامل مع الأخطاء
try:
    import win32print
    WIN32_AVAILABLE = True
except ImportError as e:
    print(f"❌ تحذير: لا يمكن استيراد win32print: {e}")
    WIN32_AVAILABLE = False

# --- Configuration File Setup ---
if platform.system() == "Windows":
    CONFIG_DIR = os.path.join(os.environ["APPDATA"], "LaraLabPrintAgent")
else:
    CONFIG_DIR = os.path.join(os.path.expanduser("~"), ".laralabprintagent")
    
if not os.path.exists(CONFIG_DIR):
    os.makedirs(CONFIG_DIR)
    
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")


class SettingsDialog(tk.Toplevel):
    def __init__(self, parent, config, on_save_callback):
        try:
            super().__init__(parent)
            self.title("إعدادات البرنامج")

            # التأكد من صحة الإعدادات
            if config is None:
                config = {}
            self.config_data = config.copy()
            self.on_save_callback = on_save_callback
            self.parent = parent

            # تحديد حجم النافذة بناءً على دقة الشاشة
            try:
                screen_width = self.winfo_screenwidth()
                screen_height = self.winfo_screenheight()

                # حساب حجم مناسب للنافذة
                dialog_width = min(450, int(screen_width * 0.4))
                dialog_height = min(500, int(screen_height * 0.6))

                # حساب موقع النافذة في وسط الشاشة
                x = (screen_width - dialog_width) // 2
                y = (screen_height - dialog_height) // 2

                self.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
            except Exception as e:
                print(f"❌ خطأ في تحديد حجم النافذة: {e}")
                self.geometry("450x500")

            self.resizable(True, True)  # السماح بتغيير الحجم لحل مشاكل العرض
            self.minsize(400, 350)  # حد أدنى للحجم

            self.create_widgets()

            # إعدادات النافذة
            self.transient(parent)
            self.grab_set()

            # التأكد من ظهور النافذة
            self.update_idletasks()
            self.deiconify()
            self.lift()
            self.focus_force()

        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة الإعدادات: {e}")
            if hasattr(self, 'parent') and self.parent:
                messagebox.showerror("خطأ", f"لا يمكن إنشاء نافذة الإعدادات:\n{e}", parent=self.parent)
            raise

    def create_widgets(self):
        try:
            frame = ttk.Frame(self, padding="10")
            frame.pack(fill="both", expand=True)

            row = 0
            # Printer Selection
            ttk.Label(frame, text="طابعة الملصقات:", anchor='e').grid(row=row, column=0, sticky='e', padx=5, pady=5)
            self.printer_var = tk.StringVar()
            self.printer_combo = ttk.Combobox(frame, textvariable=self.printer_var, state="readonly", width=30, justify='right')

            # معالجة أفضل لاستعلام الطابعات
            try:
                if WIN32_AVAILABLE:
                    printers = [printer[2] for printer in win32print.EnumPrinters(2)]
                else:
                    printers = []
                    print("❌ win32print غير متاح، لا يمكن الحصول على قائمة الطابعات")
            except Exception as e:
                print(f"❌ خطأ في الحصول على قائمة الطابعات: {e}")
                printers = []
                messagebox.showwarning("تحذير", f"لا يمكن الحصول على قائمة الطابعات:\n{e}\n\nيمكنك كتابة اسم الطابعة يدوياً.", parent=self)

            # إعداد قائمة الطابعات أو حقل نص
            if printers:
                # إذا كانت الطابعات متاحة، استخدم Combobox
                self.printer_combo['values'] = printers
                self.printer_combo.config(state="readonly")
                saved_printer = self.config_data.get("printer_name", "")
                if saved_printer in printers:
                    self.printer_var.set(saved_printer)
                else:
                    self.printer_var.set(printers[0])
            else:
                # إذا لم تكن الطابعات متاحة، اجعل الحقل قابل للتحرير
                self.printer_combo.config(state="normal")
                saved_printer = self.config_data.get("printer_name", "")
                self.printer_var.set(saved_printer if saved_printer else "أدخل اسم الطابعة يدوياً")

            self.printer_combo.grid(row=row, column=1, padx=5, pady=5, sticky='ew')
            row += 1

            # Listening Port
            ttk.Label(frame, text="منفذ الاستماع:", anchor='e').grid(row=row, column=0, sticky='e', padx=5, pady=5)
            try:
                port_value = self.config_data.get("listening_port", 9898)
                self.port_var = tk.IntVar(value=int(port_value))
            except (ValueError, TypeError):
                self.port_var = tk.IntVar(value=9898)
                print(f"❌ قيمة منفذ غير صحيحة، استخدام القيمة الافتراضية: 9898")
            ttk.Entry(frame, textvariable=self.port_var, justify='right').grid(row=row, column=1, padx=5, pady=5, sticky='ew')
            row += 1

            # Label Dimensions
            ttk.Label(frame, text="عرض الملصق (مم):", anchor='e').grid(row=row, column=0, sticky='e', padx=5, pady=5)
            try:
                width_value = self.config_data.get("label_width_mm", 28)
                self.width_var = tk.IntVar(value=int(width_value))
            except (ValueError, TypeError):
                self.width_var = tk.IntVar(value=28)
                print(f"❌ قيمة عرض غير صحيحة، استخدام القيمة الافتراضية: 28")
            ttk.Entry(frame, textvariable=self.width_var, justify='right').grid(row=row, column=1, padx=5, pady=5, sticky='ew')
            row += 1

            ttk.Label(frame, text="ارتفاع الملصق (مم):", anchor='e').grid(row=row, column=0, sticky='e', padx=5, pady=5)
            try:
                height_value = self.config_data.get("label_height_mm", 24)
                self.height_var = tk.IntVar(value=int(height_value))
            except (ValueError, TypeError):
                self.height_var = tk.IntVar(value=24)
                print(f"❌ قيمة ارتفاع غير صحيحة، استخدام القيمة الافتراضية: 24")
            ttk.Entry(frame, textvariable=self.height_var, justify='right').grid(row=row, column=1, padx=5, pady=5, sticky='ew')
            row += 1

            # DPI
            ttk.Label(frame, text="جودة الطباعة (DPI):", anchor='e').grid(row=row, column=0, sticky='e', padx=5, pady=5)
            try:
                dpi_value = self.config_data.get("dpi", 300)
                self.dpi_var = tk.IntVar(value=int(dpi_value))
            except (ValueError, TypeError):
                self.dpi_var = tk.IntVar(value=300)
                print(f"❌ قيمة DPI غير صحيحة، استخدام القيمة الافتراضية: 300")
            ttk.Entry(frame, textvariable=self.dpi_var, justify='right').grid(row=row, column=1, padx=5, pady=5, sticky='ew')
            row += 1

            # Config file path (for reference)
            ttk.Label(frame, text="مسار ملف الإعدادات:", anchor='e').grid(row=row, column=0, sticky='e', padx=5, pady=10)
            path_label = ttk.Label(frame, text=CONFIG_FILE, foreground="blue", anchor='w', justify='left', wraplength=300)
            path_label.grid(row=row, column=1, padx=5, pady=10, sticky='ew')
            row += 1

            # Configure column weights for proper resizing
            frame.columnconfigure(1, weight=1)

            # Save and Cancel buttons
            btn_frame = ttk.Frame(frame)
            btn_frame.grid(row=row, column=0, columnspan=2, pady=10)
            ttk.Button(btn_frame, text="حفظ الإعدادات", command=self.save_settings).pack(side="right", padx=10)
            ttk.Button(btn_frame, text="إلغاء", command=self.destroy).pack(side="right")

        except Exception as e:
            print(f"❌ خطأ في إنشاء واجهة الإعدادات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء نافذة الإعدادات:\n{e}", parent=self.parent)
            self.destroy()

    def save_settings(self):
        old_port = self.config_data.get("listening_port")
        new_port = self.port_var.get()
        
        self.config_data["printer_name"] = self.printer_var.get()
        self.config_data["listening_port"] = new_port
        self.config_data["label_width_mm"] = self.width_var.get()
        self.config_data["label_height_mm"] = self.height_var.get()
        self.config_data["dpi"] = self.dpi_var.get()
        
        try:
            with open(CONFIG_FILE, "w", encoding="utf-8") as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=4)
            
            messagebox.showinfo("تم الحفظ", "تم حفظ الإعدادات بنجاح.", parent=self)
            
            if old_port != new_port:
                messagebox.showinfo("إعادة تشغيل", "يجب إعادة تشغيل البرنامج لتطبيق تغييرات المنفذ.", parent=self)
            
            self.on_save_callback(self.config_data)
            self.destroy()
        except Exception as e:
            messagebox.showerror("خطأ في الحفظ", f"❌ خطأ في حفظ الإعدادات: {e}", parent=self)


class StickerPrinterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("LaraLab Print Agent")
        self.root.geometry("600x450")
        self.root.resizable(True, True)
        self.root.minsize(500, 400)

        # تحسين إعدادات العرض
        try:
            # تحديد مقياس العرض بناءً على دقة الشاشة
            screen_width = self.root.winfo_screenwidth()
            if screen_width > 1920:  # شاشات عالية الدقة
                scaling_factor = 1.4
            elif screen_width > 1366:  # شاشات متوسطة الدقة
                scaling_factor = 1.2
            else:  # شاشات منخفضة الدقة
                scaling_factor = 1.0

            self.root.tk.call('tk', 'scaling', scaling_factor)

            # تحسين عرض الخطوط العربية
            self.root.option_add('*Font', 'Tahoma 9')

        except Exception as e:
            print(f"❌ خطأ في إعدادات العرض: {e}")
            # استخدام الإعدادات الافتراضية
            try:
                self.root.tk.call('tk', 'scaling', 1.0)
            except:
                pass
        
        self.config = load_config()
        self.log_queue = queue.Queue()
        self.server_thread = None

        self.create_menu()
        self.create_main_widgets()
        self.create_status_bar()
        
        self.start_server_thread()
        self.process_log_queue()
        
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

    def create_menu(self):
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="القائمة", menu=settings_menu)
        settings_menu.add_command(label="الإعدادات", command=self.open_settings_dialog)
        settings_menu.add_separator()
        settings_menu.add_command(label="خروج", command=self.on_close)

    def create_main_widgets(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill="both", expand=True)

        log_frame = ttk.LabelFrame(main_frame, text="سجل الأحداث", padding="10")
        log_frame.pack(fill="both", expand=True)

        self.log_text = tk.Text(log_frame, height=10, width=70, state='disabled', font=("Tahoma", 9), wrap='word')
        log_scroll = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text['yscrollcommand'] = log_scroll.set
        
        self.log_text.pack(side="left", fill="both", expand=True)
        log_scroll.pack(side="right", fill="y")
        
        self.log_text.tag_config("INFO", foreground="black")
        self.log_text.tag_config("ERROR", foreground="red")
        self.log_text.tag_config("WARN", foreground="orange")
        self.log_text.tag_config("DEBUG", foreground="grey")

    def create_status_bar(self):
        status_bar = ttk.Frame(self.root, relief="sunken", padding="2 5")
        status_bar.pack(side="bottom", fill="x")
        self.server_status_label = ttk.Label(status_bar, text="الحالة: جاري البدء...", anchor="w")
        self.server_status_label.pack(side="left")

    def open_settings_dialog(self):
        try:
            # التأكد من أن النافذة الرئيسية متاحة
            if not self.root.winfo_exists():
                self.log("ERROR: النافذة الرئيسية غير متاحة")
                return

            # التأكد من وجود الإعدادات
            if not hasattr(self, 'config') or self.config is None:
                self.log("WARN: لا توجد إعدادات، استخدام الإعدادات الافتراضية")
                self.config = {
                    "printer_name": "",
                    "label_width_mm": 28,
                    "label_height_mm": 24,
                    "dpi": 300,
                    "listening_port": 9898
                }

            # إنشاء نافذة الإعدادات
            settings_dialog = SettingsDialog(self.root, self.config, self.on_settings_saved)

            # التأكد من ظهور النافذة في المقدمة
            settings_dialog.lift()
            settings_dialog.attributes('-topmost', True)
            settings_dialog.after_idle(lambda: settings_dialog.attributes('-topmost', False))

        except Exception as e:
            error_msg = f"❌ خطأ في فتح نافذة الإعدادات: {e}"
            print(error_msg)
            self.log(f"ERROR: {error_msg}")
            messagebox.showerror("خطأ", f"لا يمكن فتح نافذة الإعدادات:\n{e}")

            # محاولة إنشاء نافذة إعدادات بسيطة كبديل
            try:
                self.create_simple_settings_dialog()
            except Exception as e2:
                print(f"❌ فشل في إنشاء نافذة الإعدادات البديلة: {e2}")
                messagebox.showerror("خطأ حرج", "لا يمكن فتح أي نافذة إعدادات. يرجى إعادة تشغيل البرنامج.")

    def create_simple_settings_dialog(self):
        """نافذة إعدادات بسيطة كبديل في حالة فشل النافذة الرئيسية"""
        simple_dialog = tk.Toplevel(self.root)
        simple_dialog.title("إعدادات بسيطة")
        simple_dialog.geometry("400x300")
        simple_dialog.resizable(False, False)

        # إطار رئيسي
        main_frame = ttk.Frame(simple_dialog, padding="20")
        main_frame.pack(fill="both", expand=True)

        # رسالة تفسيرية
        info_label = ttk.Label(main_frame, text="نافذة إعدادات مبسطة\nيمكنك تعديل ملف الإعدادات مباشرة",
                              justify="center", font=("Tahoma", 10))
        info_label.pack(pady=10)

        # عرض مسار ملف الإعدادات
        path_frame = ttk.Frame(main_frame)
        path_frame.pack(fill="x", pady=10)
        ttk.Label(path_frame, text="مسار ملف الإعدادات:").pack()
        path_entry = ttk.Entry(path_frame, width=50)
        path_entry.insert(0, CONFIG_FILE)
        path_entry.config(state="readonly")
        path_entry.pack(pady=5)

        # أزرار
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(pady=20)

        def open_config_file():
            try:
                os.startfile(CONFIG_FILE)
            except Exception as e:
                messagebox.showerror("خطأ", f"لا يمكن فتح ملف الإعدادات:\n{e}")

        def open_config_folder():
            try:
                os.startfile(CONFIG_DIR)
            except Exception as e:
                messagebox.showerror("خطأ", f"لا يمكن فتح مجلد الإعدادات:\n{e}")

        ttk.Button(btn_frame, text="فتح ملف الإعدادات", command=open_config_file).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="فتح مجلد الإعدادات", command=open_config_folder).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="إغلاق", command=simple_dialog.destroy).pack(side="right", padx=5)

        # جعل النافذة في المقدمة
        simple_dialog.transient(self.root)
        simple_dialog.grab_set()
        simple_dialog.focus()

    def on_settings_saved(self, new_config):
        self.config = new_config
        self.log("INFO: ✅ تم تحديث الإعدادات.")

    def log(self, message):
        if not message: return
        
        log_level = "INFO" # Default
        if ":" in message:
            parts = message.split(":", 1)
            level = parts[0].upper()
            if level in ["INFO", "ERROR", "WARN", "DEBUG"]:
                log_level = level
                message = parts[1].strip()

        timestamp = time.strftime('%H:%M:%S')
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.configure(state='normal')
        self.log_text.insert(tk.END, formatted_message, log_level)
        self.log_text.configure(state='disabled')
        self.log_text.see(tk.END)

    def process_log_queue(self):
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.log(message)
        except queue.Empty:
            pass
        
        # Update server status
        if self.server_thread and self.server_thread.is_alive():
            port = self.config.get('listening_port', 'N/A')
            self.server_status_label.config(text=f"الحالة: الخادم يعمل على المنفذ {port}", foreground="green")
        else:
            self.server_status_label.config(text="الحالة: الخادم متوقف", foreground="red")
            
        self.root.after(250, self.process_log_queue)

    def start_server_thread(self):
        if self.server_thread and self.server_thread.is_alive():
            self.log("WARN: الخادم يعمل بالفعل.")
            return

        # The callback now includes the config and log_queue
        print_callback = lambda data: process_print_request(data, self.config, self.log_queue)

        self.server_thread = threading.Thread(
            target=start_server,
            args=(self.log_queue, self.config, print_callback),
            daemon=True
        )
        self.server_thread.start()
        self.log("INFO: تم إعطاء أمر بدء الخادم.")
        
    def on_close(self):
        if messagebox.askokcancel("خروج", "هل أنت متأكد أنك تريد الخروج؟ سيؤدي هذا إلى إيقاف خادم الطباعة."):
            # We don't need to explicitly stop the daemon thread, it will exit with the main app.
            self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = StickerPrinterApp(root)
    root.mainloop() 