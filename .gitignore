# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Virtual environments
venv/
venv_new/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Build outputs
installer_output/
release/
*.exe
*.msi
*.zip

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db
desktop.ini

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Config (keep template only)
config_local.json
