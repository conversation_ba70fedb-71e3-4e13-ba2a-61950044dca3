import os
import sys
from PIL import Image, ImageDraw, ImageFont
import arabic_reshaper
from bidi.algorithm import get_display
from datetime import datetime, timezone, timedelta
from io import BytesIO
import barcode
from barcode.writer import ImageWriter

def get_font_path(font_name):
    """Get the correct path to a font file, whether running as script or exe"""
    # Check if running as bundled executable
    if getattr(sys, 'frozen', False):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.abspath(".")
    
    font_path = os.path.join(base_path, "fonts", font_name)
    
    if os.path.exists(font_path):
        return font_path
    else:
        return None

# Arabic text reshaper configuration
arabic_reshaper_config = {
    'delete_harakat': True,
    'delete_tatweel': False,
    'support_zwj': True,
    'support_zwnj': True,
    'use_unshaped_instead_of_isolated': False,
    'shift_harakat_position': False
}
reshaper = arabic_reshaper.ArabicReshaper(configuration=arabic_reshaper_config)

def create_compact_label_image(lab_order_info, sample_group, config):
    """
    Creates a clean, compact, and well-aligned sample label (50x25mm).
    4 lines: ID-Age/Gender-Date, Name-SampleType, Barcode, Tests
    """
    # --- Config & Setup ---
    font_arabic_bold = config.get("font_bold_name", "NotoNaskhArabic-Bold.ttf")
    font_arabic_regular = config.get("font_regular_name", "NotoNaskhArabic-Regular.ttf")
    font_english_bold = config.get("font_english_bold", "arial.ttf")
    font_english_regular = config.get("font_english_regular", "arial.ttf")

    label_width_mm = float(config.get("label_width_mm", 50))
    label_height_mm = float(config.get("label_height_mm", 25))
    dpi = int(config.get("dpi", 300))
    padding_mm = config.get("padding_mm", 0.2)  # مارجن صغير
    barcode_height_mm = float(config.get("barcode_height_mm", 7.0))  # باركود
    barcode_width_factor = float(config.get("barcode_width_factor", 1.3))

    font_size_pt_patient = int(config.get("font_size_pt_patient", 8))  # تقليل خط اسم المريض
    font_size_pt_id = int(config.get("font_size_pt_id", 7))  # تقليل خط رقم العينة ونوع العينة
    font_size_pt_regular = int(config.get("font_size_pt_regular", 6))  # تقليل خط العمر والتاريخ
    font_size_pt_tests = int(config.get("font_size_pt_tests", 6))  # تقليل خط التحاليل

    line_padding_px = int(config.get("line_padding_px", 1))
    section_gap_px = int(config.get("section_gap_px", 4))

    def mm_to_px(mm):
        return int(mm * dpi / 25.4)
    def pt_to_px(pt):
        return int(pt * dpi / 72)

    label_width_px = mm_to_px(label_width_mm)
    label_height_px = mm_to_px(label_height_mm)
    padding_px = mm_to_px(padding_mm)
    barcode_height_px = mm_to_px(barcode_height_mm)
    content_width = label_width_px - padding_px * 2

    # ألوان وخلفية
    text_color = config.get("text_color", "black")
    bg_color = config.get("bg_color", "white")

    # --- 2. Font Setup (Mixed fonts: Arabic for names, English for tests) ---
    try:
        # Arabic fonts for patient names
        font_path_arabic_bold = get_font_path(font_arabic_bold)
        font_path_arabic_regular = get_font_path(font_arabic_regular)
        
        # English fonts for tests and other info
        def get_system_font_path(font_name):
            """Get system font path"""
            import os
            windows_fonts = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts')
            font_path = os.path.join(windows_fonts, font_name)
            if os.path.exists(font_path):
                return font_path
            alternatives = {
                'arial.ttf': ['arial.ttf', 'Arial.ttf', 'ARIAL.TTF'],
                'calibri.ttf': ['calibri.ttf', 'Calibri.ttf', 'CALIBRI.TTF']
            }
            for alt in alternatives.get(font_name, [font_name]):
                alt_path = os.path.join(windows_fonts, alt)
                if os.path.exists(alt_path):
                    return alt_path
            return None
        
        font_path_english_bold = get_system_font_path(font_english_bold)
        font_path_english_regular = get_system_font_path(font_english_regular)
        
        # Create fonts
        font_patient = ImageFont.truetype(font_path_arabic_bold, pt_to_px(font_size_pt_patient))  # Arabic for patient
        font_id_type = ImageFont.truetype(font_path_english_bold or font_path_arabic_bold, pt_to_px(font_size_pt_id))  # English for sample ID
        font_regular = ImageFont.truetype(font_path_english_regular or font_path_arabic_regular, pt_to_px(font_size_pt_regular))  # English for date
        font_tests = ImageFont.truetype(font_path_english_regular or font_path_arabic_regular, pt_to_px(font_size_pt_tests))  # English for tests
        
    except Exception as e:
        print(f"⚠️ Error loading fonts: {e}")
        font_patient = font_id_type = font_regular = font_tests = ImageFont.load_default()

    # --- 3. Image & Helper Setup ---
    image = Image.new("RGB", (label_width_px, label_height_px), bg_color)
    draw = ImageDraw.Draw(image)
    current_y = padding_px
    
    def process_text(text, is_arabic_context=False):
        """Enhanced text processing for mixed Arabic/English"""
        if not text:
            return ""

        text_str = str(text).strip()
        if not text_str:
            return ""

        # Check for Arabic text
        has_arabic = any('\u0600' <= char <= '\u06FF' for char in text_str)
        
        if has_arabic or is_arabic_context:
            try:
                # Enhanced Arabic text processing
                text_str = text_str.replace('أ', 'أ')  # Ensure correct Alif form
                text_str = text_str.replace('إ', 'إ')  # Alif with Hamza below
                text_str = text_str.replace('آ', 'آ')  # Alif with Madda

                # Arabic text processing
                reshaped = reshaper.reshape(text_str)
                processed = get_display(reshaped)

                return processed
            except Exception as e:
                print(f"Warning: Arabic text processing issue '{text_str}': {e}")
                return text_str
        else:
            # English text returned as-is
            return text_str

    def draw_dashed_line(y, x_start, x_end, dash_len=5, gap_len=3):
        for x in range(x_start, x_end, dash_len + gap_len):
            draw.line([(x, y), (x + dash_len, y)], fill=text_color, width=1)
        return y + line_padding_px + 1

    # --- 4. Data Extraction & Formatting ---
    patient_name = lab_order_info.get('patient', {}).get('name', 'N/A')
    patient_age_gender = lab_order_info.get('patient', {}).get('patient_age_gender', 'N/A')
    sample_code = sample_group.get('sample_code', 'N/A')
    sample_type = sample_group.get('name', 'N/A')
    test_names_str = ' '.join([test.get('name', '') for test in sample_group.get('tests', [])])

    try:
        dt_utc = datetime.fromisoformat(lab_order_info.get('created_at_raw', '').replace("Z", "+00:00"))
        # إضافة الساعة والدقيقة مع AM/PM وآخر رقمين من السنة فقط
        date_str = dt_utc.astimezone(timezone(timedelta(hours=3))).strftime("%d/%m/%y %I:%M %p")
    except (ValueError, TypeError):
        date_str = "N/A"

    # --- Section 1: Barcode (Enhanced - longer and clearer) ---
    barcode_writer = ImageWriter(format="PNG")
    ean = barcode.get('code128', sample_code, writer=barcode_writer)
    barcode_options = {
        'module_height': barcode_height_mm,
        'module_width': 0.33,  # Reduce unit width for denser barcode
        'write_text': False,
        'quiet_zone': 0.3  # Reduce quiet zone
    }
    
    barcode_fp = BytesIO()
    ean.write(barcode_fp, options=barcode_options)
    barcode_fp.seek(0)
    barcode_img = Image.open(barcode_fp)
    
    # Apply width scaling factor while maintaining height
    ratio = barcode_height_px / barcode_img.height
    new_barcode_width = int(barcode_img.width * ratio * barcode_width_factor)
    
    # Ensure barcode doesn't exceed content width
    max_barcode_width = int(content_width * 0.95)  # 95% of content width
    if new_barcode_width > max_barcode_width:
        new_barcode_width = max_barcode_width
    
    barcode_img = barcode_img.resize((new_barcode_width, barcode_height_px), Image.Resampling.LANCZOS)
    
    barcode_x = (label_width_px - new_barcode_width) // 2
    image.paste(barcode_img, (barcode_x, int(current_y)))
    current_y += barcode_height_px + section_gap_px

    # --- Section 2: Sample Code, Age/Gender & Date (English) ---
    processed_id = process_text(sample_code, is_arabic_context=False)  # Sample ID English
    processed_date = process_text(date_str, is_arabic_context=False)   # Date English
    processed_age_gender = process_text(patient_age_gender, is_arabic_context=False)  # Age/Gender English

    # رقم العينة + مسافة قصيرة + العمر/الجنس + مسافة قصيرة + التاريخ (متتالي من اليسار)
    current_x = padding_px

    # رقم العينة
    draw.text((current_x, current_y), processed_id, font=font_id_type, fill=text_color, anchor="la")
    id_width = draw.textlength(processed_id, font=font_id_type)
    current_x += id_width + mm_to_px(2)  # مسافة قصيرة 2 مم

    # العمر والجنس
    draw.text((current_x, current_y), processed_age_gender, font=font_regular, fill=text_color, anchor="la")
    age_width = draw.textlength(processed_age_gender, font=font_regular)
    current_x += age_width + mm_to_px(2)  # مسافة قصيرة 2 مم

    # التاريخ
    draw.text((current_x, current_y), processed_date, font=font_regular, fill=text_color, anchor="la")

    id_bbox = draw.textbbox((0, 0), processed_id, font=font_id_type)
    date_bbox = draw.textbbox((0, 0), processed_date, font=font_regular)
    age_gender_bbox = draw.textbbox((0, 0), processed_age_gender, font=font_regular)
    current_y += max(id_bbox[3] - id_bbox[1], date_bbox[3] - date_bbox[1], age_gender_bbox[3] - age_gender_bbox[1]) + 2  # مسافة صغيرة

    # --- Section 3: Patient Name (Arabic) & Sample Type (English) ---
    processed_type = process_text(sample_type, is_arabic_context=False)  # Sample type English

    def truncate_text_with_ellipsis(text, font, max_width):
        if draw.textlength(text, font) <= max_width:
            return text
        ellipsis = "..."
        while draw.textlength(text + ellipsis, font) > max_width and len(text) > 0:
            text = text[:-1]
        return text + ellipsis

    # مساحة أكبر للاسم مع مارجن بسيط
    name_max_width = content_width - mm_to_px(1)
    truncated_name = truncate_text_with_ellipsis(patient_name, font_patient, name_max_width)
    processed_name = process_text(truncated_name, is_arabic_context=True)  # Patient name Arabic

    # حساب الفرق في ارتفاع الخطوط لجعل نوع العينة موازي لاسم المريض
    name_bbox = draw.textbbox((0, 0), processed_name, font=font_patient)
    type_bbox = draw.textbbox((0, 0), processed_type, font=font_id_type)

    # حساب الفرق في الارتفاع لمحاذاة النصوص
    name_height = name_bbox[3] - name_bbox[1]
    type_height = type_bbox[3] - type_bbox[1]
    height_diff = (name_height - type_height) // 2  # نصف الفرق لتوسيط نوع العينة

    # اسم المريض في المنتصف (المرجع)
    center_x = label_width_px // 2
    draw.text((center_x, current_y), processed_name, font=font_patient, fill=text_color, anchor="ma")

    # نوع العينة على اليسار مع تعديل الموضع ليكون موازي للاسم
    draw.text((padding_px, current_y + height_diff), processed_type, font=font_id_type, fill=text_color, anchor="la")

    current_y += max(name_height, type_height) + 6  # إبعاد التحاليل عن الاسم

    # --- Section 5: Tests List ---
    def get_wrapped_lines(text, font, max_width):
        lines = []
        words = text.split(' ')  # تقسيم بالمسافات بدلاً من الفواصل
        if not words:
            return []

        current_line = words[0]
        for word in words[1:]:
            test_line = current_line + ' ' + word  # استخدام مسافة واحدة بدلاً من فاصلة ومسافة
            if draw.textlength(process_text(test_line), font) <= max_width:
                current_line = test_line
            else:
                lines.append(current_line)
                current_line = word
        lines.append(current_line)
        return lines

    wrapped_test_lines = get_wrapped_lines(test_names_str, font_tests, content_width)

    # Enhanced tests display (English with clear font)
    for i, line in enumerate(wrapped_test_lines):
        if not line.strip():
            continue
        if current_y < label_height_px - padding_px - 6:  # Leave enough space at bottom
             processed_line = process_text(line, is_arabic_context=False)  # Tests English
             draw.text((padding_px, current_y), processed_line, font=font_tests, fill=text_color, anchor="la")
             bbox = draw.textbbox((0,0), processed_line, font=font_tests)
             line_height = bbox[3] - bbox[1]
             # مسافة أكبر بين سطور التحاليل
             current_y += line_height + (3 if i < len(wrapped_test_lines) - 1 else 0)
        else:
             break

    return image


def get_printer_list():
    """
    Retrieves a list of available printers on the system.
    """
    import win32print
    import win32ui
    import win32con

    printers = []
    try:
        h_printers = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL, None, 2)
        for i in range(len(h_printers)):
            printers.append(h_printers[i][2])
    except Exception as e:
        print(f"Error enumerating printers: {e}")
    return printers
