# Troubleshooting Guide - LaraLab Print Agent

## 🔧 Common Issues and Solutions

### 1. **Settings Window Not Showing or Empty**

#### Possible Causes:
- Issue with printer enumeration
- Corrupted config file
- Display settings issue (DPI/Scaling)
- Problem with win32print library

#### Solutions:
1. **Restart program as administrator**
2. **Delete config file** and recreate:
   - Go to: `%APPDATA%\LaraLabPrintAgent\`
   - Delete `config.json` file
   - Restart the program

3. **Check installed printers**:
   - Ensure at least one printer is installed
   - Try printing a test page from Windows

### 2. **Program Won't Start**

#### Solutions:
1. **Install Visual C++ Redistributable**
2. **Check error log** in program folder
3. **Run from Command Prompt** to see errors

### 3. **Print Issues**

#### Solutions:
1. **Check printer connection**
2. **Update printer driver**
3. **Check printer settings** in Windows

### 4. **RPC Server Unavailable Error (1722)**

#### This is a Windows system issue, not program issue

#### Solutions:
1. **Restart Print Spooler service**:
   - Press Win + R, type: services.msc
   - Find "Print Spooler"
   - Right-click → Start

2. **Command line fix**:
   ```cmd
   net stop spooler
   net start spooler
   ```

3. **Clean print queue**:
   ```cmd
   net stop spooler
   del /Q /F C:\Windows\System32\spool\PRINTERS\*
   net start spooler
   ```

### 5. **Arabic Font Issues**

#### Solutions:
1. **Ensure fonts folder exists**
2. **Reinstall Arabic fonts**

### 6. **Port Issues**

#### Solutions:
1. **Change port number** in settings
2. **Check other programs** using same port
3. **Restart program**

## 📞 Getting Help

If issues persist:
1. Send log file
2. Mention printer type used
3. Mention Windows version
