# دليل حل المشاكل - LaraLab Print Agent

## 🔧 المشاكل الشائعة وحلولها

### 1. **نافذة الإعدادات لا تظهر أو فارغة**

#### الأسباب المحتملة:
- مشكلة في استعلام الطابعات
- ملف الإعدادات تالف
- مشكلة في إعدادات العرض (DPI/Scaling)
- مشكلة في مكتبة win32print

#### الحلول:
1. **إعادة تشغيل البرنامج كمدير**
2. **حذف ملف الإعدادات** وإعادة إنشاؤه:
   - اذهب إلى: `%APPDATA%\LaraLabPrintAgent\`
   - احذف ملف `config.json`
   - أعد تشغيل البرنامج

3. **فحص الطابعات المثبتة**:
   - تأكد من تثبيت طابعة واحدة على الأقل
   - جرب طباعة صفحة اختبار من Windows

### 2. **البرنامج لا يبدأ**

#### الحلول:
1. **تثبيت Visual C++ Redistributable**
2. **فحص ملف الخطأ** في مجلد البرنامج
3. **تشغيل من Command Prompt** لرؤية الأخطاء

### 3. **مشاكل الطباعة**

#### الحلول:
1. **فحص اتصال الطابعة**
2. **تحديث تعريف الطابعة**
3. **فحص إعدادات الطابعة** في Windows

### 4. **مشاكل الخطوط العربية**

#### الحلول:
1. **تأكد من وجود مجلد fonts**
2. **إعادة تثبيت الخطوط العربية**

### 5. **مشكلة في المنفذ (Port)**

#### الحلول:
1. **تغيير رقم المنفذ** في الإعدادات
2. **فحص البرامج الأخرى** التي تستخدم نفس المنفذ
3. **إعادة تشغيل البرنامج**

## 📞 الحصول على المساعدة

إذا استمرت المشاكل:
1. أرسل ملف السجل (log)
2. اذكر نوع الطابعة المستخدمة
3. اذكر إصدار Windows
