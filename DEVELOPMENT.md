# Development Guide - LaraLab Print Agent

## 🏗️ Project Architecture

### Core Components

1. **`gui.py`** - Main GUI application
   - Tkinter-based interface
   - Settings management
   - Server status monitoring

2. **`server.py`** - Flask web server
   - HTTP API endpoint
   - JSON request handling
   - Print job routing

3. **`printer_core.py`** - Core printing logic
   - Windows printer integration
   - Print Spooler service management
   - Error handling and recovery

4. **`label_design.py`** - Label generation
   - Arabic text rendering
   - Barcode generation
   - Image composition

## 🔧 Development Setup

### Prerequisites
```bash
# Python 3.8+
python --version

# Install dependencies
pip install -r requirements.txt

# For building
pip install pyinstaller
```

### Running Development Version
```bash
python gui.py
```

### Testing
```bash
# Test printing functionality
python simple_print_test.py

# Test server endpoint
curl -X POST http://localhost:9898/print -H "Content-Type: application/json" -d @test_data.json
```

## 🔄 Build Process

### Quick Build
```bash
python build_installer.py
```

### Manual Build Steps
1. **Clean previous builds**
   ```bash
   rm -rf build dist
   ```

2. **Build executable**
   ```bash
   pyinstaller --onefile --windowed --name=LaraLabPrintAgent --add-data="fonts;fonts" --add-data="config.json;." gui.py
   ```

3. **Create installer**
   ```bash
   "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" simple_installer.iss
   ```

## 📝 Code Style

### Naming Conventions
- Functions: `snake_case`
- Classes: `PascalCase`
- Constants: `UPPER_CASE`
- Variables: `snake_case`

### Error Handling
- Always use try-catch blocks for external operations
- Log errors with descriptive messages
- Provide user-friendly error messages
- Include recovery suggestions

### Logging
- Use consistent log levels: INFO, WARN, ERROR, DEBUG
- Include emojis for visual clarity: ✅ ❌ ⚠️ 🔧
- Provide actionable information

## 🧪 Testing

### Unit Testing
```bash
# Add unit tests in tests/ directory
python -m pytest tests/
```

### Integration Testing
```bash
# Test with actual printer
python simple_print_test.py

# Test server endpoint
python -c "import requests; print(requests.post('http://localhost:9898/print', json={...}))"
```

## 🚀 Deployment

### For End Users
1. Build installer: `python build_installer.py`
2. Distribute: `LaraLabPrintAgent_vX.X_English_Final.exe`

### For Development
1. Use virtual environment
2. Install in development mode
3. Use configuration files for different environments

## 🔍 Debugging

### Common Issues
- **Print Spooler**: Check Windows service status
- **Fonts**: Ensure Arabic fonts are in fonts/ directory
- **Permissions**: Run as administrator if needed
- **Ports**: Check for port conflicts

### Debug Mode
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📋 Future Improvements

### Planned Features
- [ ] Web-based configuration interface
- [ ] Multiple printer support
- [ ] Print queue management
- [ ] Network printer support
- [ ] Print history logging

### Code Improvements
- [ ] Add comprehensive unit tests
- [ ] Implement configuration validation
- [ ] Add print preview functionality
- [ ] Improve error recovery mechanisms

## 📞 Contributing

1. Follow existing code style
2. Add tests for new features
3. Update documentation
4. Test on multiple Windows versions
5. Ensure Arabic text rendering works correctly
