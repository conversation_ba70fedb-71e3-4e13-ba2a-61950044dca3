# automatically generated by the FlatBuffers compiler, do not modify

# namespace: structure

import flatbuffers


class TensorMap(object):
  __slots__ = ['_tab']

  @classmethod
  def GetRootAsTensorMap(cls, buf, offset):
    n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
    x = TensorMap()
    x.Init(buf, n + offset)
    return x

  # TensorMap
  def Init(self, buf, pos):
    self._tab = flatbuffers.table.Table(buf, pos)

  # TensorMap
  def Name(self):
    o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
    if o != 0:
      return self._tab.String(o + self._tab.Pos)
    return None

  # TensorMap
  def Info(self):
    o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
    if o != 0:
      return self._tab.String(o + self._tab.Pos)
    return None

  # TensorMap
  def Tensors(self, j):
    o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
    if o != 0:
      x = self._tab.Vector(o)
      x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
      x = self._tab.Indirect(x)
      from .Tensor import Tensor
      obj = Tensor()
      obj.Init(self._tab.Bytes, x)
      return obj
    return None

  # TensorMap
  def TensorsLength(self):
    o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
    if o != 0:
      return self._tab.VectorLen(o)
    return 0


def TensorMapStart(builder): builder.StartObject(3)


def TensorMapAddName(builder, name): builder.PrependUOffsetTRelativeSlot(
    0, flatbuffers.number_types.UOffsetTFlags.py_type(name), 0)


def TensorMapAddInfo(builder, info): builder.PrependUOffsetTRelativeSlot(
    1, flatbuffers.number_types.UOffsetTFlags.py_type(info), 0)


def TensorMapAddTensors(builder, tensors): builder.PrependUOffsetTRelativeSlot(
    2, flatbuffers.number_types.UOffsetTFlags.py_type(tensors), 0)


def TensorMapStartTensorsVector(
    builder, numElems): return builder.StartVector(4, numElems, 4)


def TensorMapEnd(builder): return builder.EndObject()
